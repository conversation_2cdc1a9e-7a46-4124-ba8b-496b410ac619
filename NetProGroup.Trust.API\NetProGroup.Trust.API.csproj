﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>disable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<UserSecretsId>d78554a8-1818-4338-8e12-2a797729f37d</UserSecretsId>
		<RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
		<FileVersion>1.5.1</FileVersion>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="App_Data\logs\**" />
		<Content Remove="App_Data\logs\**" />
		<EmbeddedResource Remove="App_Data\logs\**" />
		<None Remove="App_Data\logs\**" />
	</ItemGroup>

	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1014;CA1716</NoWarn>
		<!-- CA1014 == CLS Compliancy, not required -->
		<!-- CA1716 == Don't use Shared keyword in namespace. -->
	</PropertyGroup>

	<ItemGroup>
		<Content Remove="stylecop.json" />
	</ItemGroup>

	<ItemGroup>
		<AdditionalFiles Include="stylecop.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
		<PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.3.0" />
		<PackageReference Include="NetPro.StyleCop.Configuration.Package" Version="1.1.24" />
		<PackageReference Include="NetProGroup.Framework" Version="1.4.5" />
		<PackageReference Include="OneOf" Version="3.0.271" />
		<PackageReference Include="OneOf.SourceGenerator" Version="3.0.271" />
		<PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\NetProGroup.Trust.Application.Contracts\NetProGroup.Trust.Application.Contracts.csproj" />
		<ProjectReference Include="..\NetProGroup.Trust.Application\NetProGroup.Trust.Application.csproj" />
		<ProjectReference Include="..\NetProGroup.Trust.DataManager\NetProGroup.Trust.DataManager.csproj" />
		<ProjectReference Include="..\NetProGroup.Trust.Payment\NetProGroup.Trust.Payment.csproj" />
		<ProjectReference Include="..\NetProGroup.Trust.Reports\NetProGroup.Trust.Reports.csproj" />
	</ItemGroup>

</Project>
